package br.com.pacto.controller.json.atividade;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.atividade.AtividadeCrossfitService;
import br.com.pacto.swagger.respostas.atividade.crossfit.ExemploRespostaAtividadeCrossfitResponseDTO;
import br.com.pacto.swagger.respostas.atividade.crossfit.ExemploRespostaListAtividadeCrossfitResponseDTO;
import br.com.pacto.swagger.respostas.variaveis.ExemploRespostaBoolean;
import io.swagger.annotations.*;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.logging.Level;
import java.util.logging.Logger;


/**
 * <AUTHOR> 31/01/2019
 */
@Controller
@RequestMapping("/psec/atividades-crossfit")
public class AtividadeCrossfitController {

    private final AtividadeCrossfitService atividadeCrossfitService;

    @Autowired
    public AtividadeCrossfitController(final AtividadeCrossfitService atividadeCrossfitService) {
        Assert.notNull(atividadeCrossfitService, "O serviço da atividade crossfit não foi injetado corretamente");
        this.atividadeCrossfitService = atividadeCrossfitService;
    }

    @ApiOperation(
            value = "Criar atividade física CrossFit",
            notes = "Cria uma nova atividade física do tipo CrossFit.",
            tags = "Atividades"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "empresaId", value = "Código identificador da empresa", required = true, paramType = "header", dataType = "long", example = "1")
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaAtividadeCrossfitResponseDTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.ATIVIDADES_WOD)
    @RequestMapping(method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> criarAtividadeCrossfit(@ApiParam(value = "Informações para criar uma atividade CrossFit. Não é necessário informar o codigo para criação.") @RequestBody AtividadeCrossfitDTO atividadeCrossfitDTO) {
        try {
            return ResponseEntityFactory.ok(atividadeCrossfitService.insert(atividadeCrossfitDTO));
        } catch (ServiceException e) {
            Logger.getLogger(AtividadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar criar atividade do crossfit", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @ApiOperation(
            value = "Atualizar atividade física CrossFit",
            notes = "Atualiza as informaçcões de uma atividade física do tipo CrossFit.",
            tags = "Atividades"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaAtividadeCrossfitResponseDTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.ATIVIDADES_WOD)
    @RequestMapping(value = "/{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> atualizarAtividadeCrossfit(
            @ApiParam(value = "Código da atividade CrossFit que será atualizada", defaultValue = "1", required = true)
            @PathVariable("id") Integer id,
            @ApiParam(value = "Informações da atividade CrossFit que será atualizada. Não é necessário informar o código para realizar a requisição. Apenas o ID no PATH da requisição.")
            @RequestBody AtividadeCrossfitDTO atividadeCrossfitDTO) {
        try {
            atividadeCrossfitDTO.setId(id);
            return ResponseEntityFactory.ok(atividadeCrossfitService.update(atividadeCrossfitDTO));
        } catch (ServiceException e) {
            Logger.getLogger(AtividadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar atualizar atividade do crossfit", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Consultar atividade física CrossFit",
            notes = "Consulta as informaçcões de uma atividade física do tipo CrossFit.",
            tags = "Atividades"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaAtividadeCrossfitResponseDTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.ATIVIDADES_WOD)
    @RequestMapping(value = "/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> consultarAtividadeCrossfit(
            @ApiParam(value = "Código da atividade que será consultada", required = true, defaultValue = "1")
            @PathVariable("id") Integer id) {
        try {
            return ResponseEntityFactory.ok(atividadeCrossfitService.obterPorId(id));
        } catch (ServiceException e) {
            Logger.getLogger(AtividadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar consultar atividade do crossfit", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @ApiOperation(
            value = "Consultar atividades físicas do CrossFit",
            notes = "Consulta as informaçcões de uma atividade física do tipo CrossFit.",
            tags = "Atividades"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "Página da requisição", defaultValue = "1", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "size", value = "Quantidade total de elementos que são procurados", defaultValue = "10", paramType = "query", dataType = "long"),
            @ApiImplicitParam(name = "sort", value = "Ordenação das respostas.<br/> Ordena por um atributo contido no objeto da resposta." +
                    "<strong>Ordens disponíveis</strong>" +
                    "<ul>" +
                    "<li>ASC - Ordena pelo atributo escolhido de forma ascendente</li>" +
                    "<li>DESC - Ordena pelo atributo escolhido de forma descendente</li>" +
                    "</ul>" +
                    "Deve ser informado como atributo,ordem", defaultValue = "nome,asc", paramType = "query", dataType = "string"),
    })
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaListAtividadeCrossfitResponseDTO.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.ATIVIDADES_WOD)
    @RequestMapping(method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarAtividadeCrossfit(
            @ApiParam(value = "Filtros de busca, deve ser informado como um JSON.\n\n" +
                    "<strong>É necessário fazer o ENCODE na URL para realizar a requisição com os filtros.</strong>\n\n" +
                    "<strong>Filtros disponíveis:</strong>\n\n" +
                    "- <strong>quicksearchValue:</strong>Filtra pelo nome da atividade\n" +
                    "- <strong>quicksearchFields:</strong>Define em qual campo o 'quicksearchValue' será aplicado (Para filtrar pelo nome, deve ser informado como uma lista ex: [\"nome\"])\n",
                    defaultValue = "{\"quicksearchValue\":\"Funcional\", \"quicksearchFields\":[\"nome\"]}")
            @RequestParam(value = "filters", required = false) JSONObject filtros,
            @ApiIgnore PaginadorDTO paginadorDTO) throws JSONException {
        try {
            FiltroAtividadeCrossfitJSON filtroAtividadeCrossfitJSON = new FiltroAtividadeCrossfitJSON(filtros);
            return ResponseEntityFactory.ok(atividadeCrossfitService.listarAtividadesCrossfit(filtroAtividadeCrossfitJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(AtividadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar atividades do crossfit", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ApiOperation(
            value = "Deletar atividade física CrossFit",
            notes = "Exclui as informaçcões de uma atividade física do tipo CrossFit.",
            tags = "Atividades"
    )
    @ApiResponses({
            @ApiResponse(code = 200, message = "Resposta 200 (Solicitação bem-sucedida)", response = ExemploRespostaBoolean.class)
    })
    @ResponseBody
    @Permissao(recursos = RecursoEnum.ATIVIDADES_WOD)
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> inativarAtividadeCrossfit(
            @ApiParam(value = "Código da atividade crossfit que será excluída", defaultValue = "1", required = true)
            @PathVariable("id") Integer id) {
        try {
            atividadeCrossfitService.excluir(id);
            return ResponseEntityFactory.ok(true);
        } catch (ServiceException e) {
            Logger.getLogger(AtividadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar inativar atividade do crossfit", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
}
